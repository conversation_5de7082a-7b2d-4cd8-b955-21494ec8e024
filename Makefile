# DMTools Makefile
# Builds all dmtools components: dmscreen (Wails app) and localscribe (Go binary)

.PHONY: all build clean install test deps help
.PHONY: localscribe localscribe-clean localscribe-test localscribe-install
.PHONY: dmscreen dmscreen-clean dmscreen-dev dmscreen-frontend
.PHONY: check-wails check-node

# Default target
all: deps build

# Build all components
build: localscribe dmscreen

# Install dependencies for all components
deps: localscribe-deps dmscreen-deps

# Clean all build artifacts
clean: localscribe-clean dmscreen-clean

# Install all binaries
install: localscribe-install dmscreen-install

# Run tests for all components
test: localscribe-test

# Help target
help:
	@echo "DMTools Build System"
	@echo "===================="
	@echo ""
	@echo "Main targets:"
	@echo "  all              - Build all components (default)"
	@echo "  build            - Build all components"
	@echo "  deps             - Install dependencies for all components"
	@echo "  clean            - Clean all build artifacts"
	@echo "  install          - Install all binaries"
	@echo "  test             - Run tests for all components"
	@echo ""
	@echo "Localscribe targets:"
	@echo "  localscribe      - Build localscribe binary"
	@echo "  localscribe-deps - Install localscribe dependencies"
	@echo "  localscribe-clean - Clean localscribe build artifacts"
	@echo "  localscribe-test - Run localscribe tests"
	@echo "  localscribe-install - Install localscribe binary"
	@echo ""
	@echo "DMScreen targets:"
	@echo "  dmscreen         - Build dmscreen Wails app"
	@echo "  dmscreen-deps    - Install dmscreen dependencies"
	@echo "  dmscreen-clean   - Clean dmscreen build artifacts"
	@echo "  dmscreen-dev     - Run dmscreen in development mode"
	@echo "  dmscreen-frontend - Build only the frontend"
	@echo "  dmscreen-embed-binaries - Embed binaries in dmscreen assets"
	@echo ""
	@echo "Development targets:"
	@echo "  dev              - Run dmscreen in development mode"
	@echo "  quick-build      - Quick build (skips some checks)"
	@echo "  release          - Build optimized release versions"
	@echo "  run-localscribe  - Build and run localscribe for testing"
	@echo "  package          - Package binaries for distribution"
	@echo ""
	@echo "Utility targets:"
	@echo "  status           - Show build status"
	@echo "  check-wails      - Check if Wails is installed"
	@echo "  check-node       - Check if Node.js is installed"

# =============================================================================
# Localscribe targets
# =============================================================================

localscribe:
	@echo "Building localscribe..."
	$(MAKE) -C localscribe build

localscribe-deps:
	@echo "Installing localscribe dependencies..."
	$(MAKE) -C localscribe deps

localscribe-clean:
	@echo "Cleaning localscribe..."
	$(MAKE) -C localscribe clean

localscribe-test:
	@echo "Testing localscribe..."
	$(MAKE) -C localscribe test

localscribe-install: localscribe
	@echo "Installing localscribe..."
	$(MAKE) -C localscribe install

# =============================================================================
# DMScreen targets
# =============================================================================

dmscreen: check-wails dmscreen-deps dmscreen-embed-binaries
	@echo "Building dmscreen..."
	cd dmscreen && ~/go/bin/wails build

dmscreen-deps: check-node
	@echo "Installing dmscreen dependencies..."
	cd dmscreen/frontend && npm install
	cd dmscreen && go mod download && go mod tidy

dmscreen-clean:
	@echo "Cleaning dmscreen..."
	cd dmscreen && rm -rf build/bin/ assets/bin/
	cd dmscreen/frontend && rm -rf dist/ node_modules/

dmscreen-dev: check-wails dmscreen-deps
	@echo "Starting dmscreen in development mode..."
	cd dmscreen && ~/go/bin/wails dev

dmscreen-frontend: check-node
	@echo "Building dmscreen frontend..."
	cd dmscreen/frontend && npm run build

dmscreen-embed-binaries: localscribe
	@echo "Embedding binaries in dmscreen..."
	@mkdir -p dmscreen/assets/bin
	@if [ -f localscribe/bin/localscribe ]; then \
		cp localscribe/bin/localscribe dmscreen/assets/bin/; \
		echo "Embedded localscribe binary"; \
	else \
		echo "Warning: localscribe binary not found, skipping embed"; \
	fi

dmscreen-install: dmscreen
	@echo "Installing dmscreen..."
	@if [ -f dmscreen/build/bin/dmscreen ]; then \
		cp dmscreen/build/bin/dmscreen /usr/local/bin/ || \
		echo "Note: Could not install to /usr/local/bin (try with sudo)"; \
	else \
		echo "Error: dmscreen binary not found. Run 'make dmscreen' first."; \
		exit 1; \
	fi

# =============================================================================
# Utility targets
# =============================================================================

check-wails:
	@if ! command -v ~/go/bin/wails >/dev/null 2>&1; then \
		echo "Error: Wails not found at ~/go/bin/wails"; \
		echo "Please install Wails: go install github.com/wailsapp/wails/v2/cmd/wails@latest"; \
		exit 1; \
	fi

check-node:
	@if ! command -v node >/dev/null 2>&1; then \
		echo "Error: Node.js not found"; \
		echo "Please install Node.js from https://nodejs.org/"; \
		exit 1; \
	fi
	@if ! command -v npm >/dev/null 2>&1; then \
		echo "Error: npm not found"; \
		echo "Please install npm (usually comes with Node.js)"; \
		exit 1; \
	fi

# =============================================================================
# Development shortcuts
# =============================================================================

dev: dmscreen-dev

# Quick build for development (skips some checks)
quick-build:
	$(MAKE) -C localscribe build
	cd dmscreen && ~/go/bin/wails build -s

# Build release versions with optimizations
release: clean
	@echo "Building release versions..."
	$(MAKE) -C localscribe build
	cd dmscreen && ~/go/bin/wails build -clean -upx

# Build and run localscribe for testing
run-localscribe: localscribe
	@echo "Running localscribe (Ctrl+C to stop)..."
	./localscribe/bin/localscribe

# Package binaries for distribution
package: build
	@echo "Packaging dmtools..."
	@mkdir -p dist
	@cp localscribe/bin/localscribe dist/
	@# Handle macOS .app bundle or Linux binary
	@if [ -f dmscreen/build/bin/dmscreen.app/Contents/MacOS/dmscreen ]; then \
		cp dmscreen/build/bin/dmscreen.app/Contents/MacOS/dmscreen dist/dmscreen; \
		echo "Copied macOS binary from .app bundle"; \
	elif [ -f dmscreen/build/bin/dmscreen ]; then \
		cp dmscreen/build/bin/dmscreen dist/; \
		echo "Copied Linux binary"; \
	else \
		echo "Warning: dmscreen binary not found"; \
	fi
	@echo "Binaries packaged in dist/ directory"
	@ls -la dist/

# Show build status
status:
	@echo "DMTools Build Status"
	@echo "===================="
	@echo ""
	@echo "Localscribe:"
	@if [ -f localscribe/bin/localscribe ]; then \
		echo "  ✓ Binary exists: localscribe/bin/localscribe"; \
		ls -la localscribe/bin/localscribe; \
	else \
		echo "  ✗ Binary not found"; \
	fi
	@echo ""
	@echo "DMScreen:"
	@if [ -f dmscreen/build/bin/dmscreen.app/Contents/MacOS/dmscreen ]; then \
		echo "  ✓ Binary exists: dmscreen/build/bin/dmscreen.app/Contents/MacOS/dmscreen"; \
		ls -la dmscreen/build/bin/dmscreen.app/Contents/MacOS/dmscreen; \
	elif [ -f dmscreen/build/bin/dmscreen ]; then \
		echo "  ✓ Binary exists: dmscreen/build/bin/dmscreen"; \
		ls -la dmscreen/build/bin/dmscreen; \
	else \
		echo "  ✗ Binary not found"; \
	fi
	@echo ""
	@echo "Dependencies:"
	@if command -v ~/go/bin/wails >/dev/null 2>&1; then \
		echo "  ✓ Wails installed"; \
	else \
		echo "  ✗ Wails not found"; \
	fi
	@if command -v node >/dev/null 2>&1; then \
		echo "  ✓ Node.js installed"; \
	else \
		echo "  ✗ Node.js not found"; \
	fi
	@if command -v npm >/dev/null 2>&1; then \
		echo "  ✓ npm installed"; \
	else \
		echo "  ✗ npm not found"; \
	fi
