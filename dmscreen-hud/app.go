package main

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

// HUDMessage represents a message displayed in the HUD
type HUDMessage struct {
	ID        int64     `json:"id"`
	Type      string    `json:"type"`
	Content   string    `json:"content"`
	ImagePath string    `json:"imagePath,omitempty"`
	Timestamp time.Time `json:"timestamp"`
}

// AppSettings represents the application settings/preferences
type AppSettings struct {
	DNDVersion    string `json:"dndVersion"`    // "2014" or "2024"
	GeminiAPIKey  string `json:"geminiAPIKey"`  // Stored securely, masked in UI
	AssemblyAIKey string `json:"assemblyAIKey"` // Stored securely, masked in UI
}

// App struct
type App struct {
	ctx                 context.Context
	messages            []HUDMessage
	isVisible           bool
	localscribeProcess  *exec.Cmd
	localscribeRunning  bool
	localscribeMutex    sync.Mutex
	textCanvasBuffer    []string
	textCanvasMutex     sync.Mutex
	logFilePath         string
	logFileWatcher      *os.File
	logFileOffset       int64
	settings            AppSettings
	settingsMutex       sync.Mutex
	embeddedBinaryPaths map[string]string
	embeddedBinaryMutex sync.Mutex
}

// NewApp creates a new App application struct
func NewApp() *App {
	return &App{
		messages:            make([]HUDMessage, 0),
		isVisible:           true,
		textCanvasBuffer:    make([]string, 0),
		embeddedBinaryPaths: make(map[string]string),
		settings: AppSettings{
			DNDVersion:    "2024", // Default to 2024 edition
			GeminiAPIKey:  os.Getenv("GEMINI_API_KEY"),
			AssemblyAIKey: os.Getenv("ASSEMBLYAI_API_KEY"),
		},
	}
}

// startup is called when the app starts. The context is saved
// so we can call the runtime methods
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx
	fmt.Println("DM Screen HUD started")

	// Start the global hotkey listener for Cmd+D
	go func() {
		if err := StartHotkeyListener(ctx, func() {
			// Toggle window visibility when Cmd+D is pressed
			ToggleWindowVisibility(a.ctx, &a.isVisible)
		}); err != nil {
			fmt.Printf("Hotkey listener error: %v\n", err)
		}
	}()
}

// AddMessage adds a new message to the HUD
func (a *App) AddMessage(msgType, content string) HUDMessage {
	message := HUDMessage{
		ID:        time.Now().UnixNano(),
		Type:      msgType,
		Content:   content,
		Timestamp: time.Now(),
	}

	a.messages = append([]HUDMessage{message}, a.messages...) // Prepend to slice

	// Keep only the last 50 messages to prevent memory issues
	if len(a.messages) > 50 {
		a.messages = a.messages[:50]
	}

	return message
}

// GetMessages returns all current messages
func (a *App) GetMessages() []HUDMessage {
	return a.messages
}

// ClearMessages removes all messages
func (a *App) ClearMessages() {
	a.messages = make([]HUDMessage, 0)
}

// SendToGallery handles sending content to the gallery
func (a *App) SendToGallery(messageID int64) string {
	for _, msg := range a.messages {
		if msg.ID == messageID {
			// TODO: Implement actual gallery integration
			fmt.Printf("Sending to gallery: %s - %s\n", msg.Type, msg.Content)
			return fmt.Sprintf("Sent '%s' to gallery", msg.Type)
		}
	}
	return "Message not found"
}

// LogMessage handles logging a message
func (a *App) LogMessage(messageID int64) string {
	for _, msg := range a.messages {
		if msg.ID == messageID {
			// TODO: Implement actual logging functionality
			fmt.Printf("Logging message: %s - %s\n", msg.Type, msg.Content)
			return fmt.Sprintf("Logged '%s' message", msg.Type)
		}
	}
	return "Message not found"
}

// ToggleVisibility toggles the window visibility (can be called from frontend)
func (a *App) ToggleVisibility() {
	ToggleWindowVisibility(a.ctx, &a.isVisible)
}

// EnableHotkeyDebug enables debug logging for hotkeys
func (a *App) EnableHotkeyDebug() {
	DebugHotkeys = true
	fmt.Println("Hotkey debug mode enabled")
}

// GetVisibilityState returns the current visibility state
func (a *App) GetVisibilityState() bool {
	return a.isVisible
}

// StartLocalscribe starts the localscribe process
func (a *App) StartLocalscribe() string {
	a.localscribeMutex.Lock()
	defer a.localscribeMutex.Unlock()

	if a.localscribeRunning {
		return "Localscribe is already running"
	}

	// Check if AssemblyAI API key is available
	a.settingsMutex.Lock()
	assemblyAIKey := a.settings.AssemblyAIKey
	a.settingsMutex.Unlock()

	if assemblyAIKey == "" {
		return "AssemblyAI API key is required. Please set it in Preferences first."
	}

	// Find the localscribe binary
	localscribePath, err := a.findLocalscribeBinary()
	if err != nil {
		return fmt.Sprintf("Error finding localscribe binary: %v", err)
	}

	// Create a specific log file for this session
	logFilePath, err := a.createSessionLogFile()
	if err != nil {
		return fmt.Sprintf("Failed to create log file: %v", err)
	}

	// Create the command with transcript-only mode and specific log file
	cmd := exec.Command(localscribePath, "-t", "-l", logFilePath)

	// Set environment variables using the saved AssemblyAI key
	cmd.Env = append(os.Environ(), "ASSEMBLYAI_API_KEY="+assemblyAIKey)

	// Add debug logging
	fmt.Printf("Starting localscribe with:\n")
	fmt.Printf("  Binary: %s\n", localscribePath)
	fmt.Printf("  Args: %v\n", cmd.Args)
	fmt.Printf("  Log file: %s\n", logFilePath)
	fmt.Printf("  AssemblyAI key: %s\n", a.maskAPIKey(assemblyAIKey))
	fmt.Printf("  Environment variables count: %d\n", len(cmd.Env))

	// Check if the API key is actually in the environment
	for _, env := range cmd.Env {
		if strings.HasPrefix(env, "ASSEMBLYAI_API_KEY=") {
			fmt.Printf("  Found ASSEMBLYAI_API_KEY in environment: %s\n", a.maskAPIKey(strings.TrimPrefix(env, "ASSEMBLYAI_API_KEY=")))
			break
		}
	}

	// Capture stdout/stderr for debugging
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return fmt.Sprintf("Error creating stdout pipe: %v", err)
	}
	stderr, err := cmd.StderrPipe()
	if err != nil {
		return fmt.Sprintf("Error creating stderr pipe: %v", err)
	}
	cmd.Stdin = nil

	// Start the process
	if err := cmd.Start(); err != nil {
		return fmt.Sprintf("Error starting localscribe: %v", err)
	}

	fmt.Printf("Localscribe process started with PID: %d\n", cmd.Process.Pid)

	a.localscribeProcess = cmd
	a.localscribeRunning = true

	// Start reading stdout and stderr for debugging
	go a.readLocalscribeOutput(stdout, "STDOUT")
	go a.readLocalscribeOutput(stderr, "STDERR")

	// Start log file tailing for our specific log file
	go func() {
		fmt.Printf("Waiting for localscribe to start writing to log file...\n")
		time.Sleep(2 * time.Second) // Give localscribe time to start

		fmt.Printf("Starting to tail log file: %s\n", logFilePath)
		if err := a.startLogFileTailing(logFilePath); err != nil {
			fmt.Printf("Failed to start tailing log file: %v\n", err)
		}
	}()

	// Monitor process completion
	go func() {
		err := cmd.Wait()
		if err != nil {
			fmt.Printf("Localscribe process ended with error: %v\n", err)
		} else {
			fmt.Printf("Localscribe process ended normally\n")
		}

		// Check exit code
		if exitError, ok := err.(*exec.ExitError); ok {
			fmt.Printf("Localscribe exit code: %d\n", exitError.ExitCode())
		}

		a.localscribeMutex.Lock()
		a.localscribeRunning = false
		a.localscribeProcess = nil
		a.localscribeMutex.Unlock()
	}()

	return "Localscribe started successfully"
}

// StopLocalscribe stops the localscribe process
func (a *App) StopLocalscribe() string {
	a.localscribeMutex.Lock()
	defer a.localscribeMutex.Unlock()

	if !a.localscribeRunning || a.localscribeProcess == nil {
		return "Localscribe is not running"
	}

	// Send interrupt signal to gracefully stop
	if err := a.localscribeProcess.Process.Signal(os.Interrupt); err != nil {
		// If interrupt fails, force kill
		a.localscribeProcess.Process.Kill()
	}

	a.localscribeRunning = false
	a.localscribeProcess = nil

	return "Localscribe stopped"
}

// IsLocalscribeRunning returns whether localscribe is currently running
func (a *App) IsLocalscribeRunning() bool {
	a.localscribeMutex.Lock()
	defer a.localscribeMutex.Unlock()
	return a.localscribeRunning
}

// findLocalscribeBinary locates the localscribe binary
func (a *App) findLocalscribeBinary() (string, error) {
	// First, try to use embedded binary
	a.embeddedBinaryMutex.Lock()
	embeddedPath, err := a.GetEmbeddedBinaryPath("localscribe")
	a.embeddedBinaryMutex.Unlock()

	if err == nil {
		fmt.Printf("Using embedded localscribe binary: %s\n", embeddedPath)

		// Test the embedded binary
		fmt.Printf("Testing embedded binary: %s\n", embeddedPath)
		testCmd := exec.Command(embeddedPath, "--help")
		testOutput, testErr := testCmd.CombinedOutput()
		if testErr != nil {
			fmt.Printf("Warning: Embedded binary test failed: %v\n", testErr)
			fmt.Printf("Test output: %s\n", string(testOutput))
		} else {
			fmt.Printf("Embedded binary test successful\n")
		}

		return embeddedPath, nil
	}

	fmt.Printf("Embedded binary not available (%v), trying fallback paths...\n", err)

	// Fallback: try to find it relative to the current executable
	execPath, err := os.Executable()
	if err == nil {
		execDir := filepath.Dir(execPath)
		localscribePath := filepath.Join(execDir, "..", "localscribe", "bin", "localscribe")
		if _, err := os.Stat(localscribePath); err == nil {
			fmt.Printf("Using relative localscribe binary: %s\n", localscribePath)
			return localscribePath, nil
		}
	}

	// Try relative to working directory
	localscribePath := filepath.Join("..", "localscribe", "bin", "localscribe")
	if _, err := os.Stat(localscribePath); err == nil {
		fmt.Printf("Using working directory localscribe binary: %s\n", localscribePath)
		return localscribePath, nil
	}

	// Try in PATH
	if path, err := exec.LookPath("localscribe"); err == nil {
		fmt.Printf("Using PATH localscribe binary: %s\n", path)
		return path, nil
	}

	return "", fmt.Errorf("localscribe binary not found in embedded assets, relative paths, or PATH")
}

// readLocalscribeOutput reads output from localscribe and sends it to the text canvas
func (a *App) readLocalscribeOutput(pipe io.ReadCloser, source string) {
	defer pipe.Close()
	scanner := bufio.NewScanner(pipe)
	for scanner.Scan() {
		line := scanner.Text()
		// Log to console for debugging
		fmt.Printf("[%s] %s\n", source, line)
		// Send this line to the frontend text canvas for debugging
		a.sendToTextCanvas(fmt.Sprintf("[%s] %s", source, line))
	}
}

// sendToTextCanvas sends a line of text to the frontend text canvas (internal method)
func (a *App) sendToTextCanvas(text string) {
	a.textCanvasMutex.Lock()

	// Add timestamp
	timestampedText := fmt.Sprintf("[%s] %s", time.Now().Format("15:04:05"), text)

	// Add to buffer
	a.textCanvasBuffer = append(a.textCanvasBuffer, timestampedText)

	// Keep only the last 10,000 lines to handle 4+ hour sessions
	// At ~25 lines per KB, this supports ~400KB of text (6+ hour sessions)
	if len(a.textCanvasBuffer) > 10000 {
		a.textCanvasBuffer = a.textCanvasBuffer[len(a.textCanvasBuffer)-10000:]
	}

	a.textCanvasMutex.Unlock()

	// Emit event to frontend with the new line (no polling needed!)
	if a.ctx != nil {
		// Import will be added when we use runtime.EventsEmit
		// runtime.EventsEmit(a.ctx, "textCanvasUpdate", timestampedText)
		// For now, we'll keep the polling approach but make it much less frequent
	}
}

// GetTextCanvasContent returns all text canvas content
func (a *App) GetTextCanvasContent() []string {
	a.textCanvasMutex.Lock()
	defer a.textCanvasMutex.Unlock()

	// Return a copy of the buffer
	result := make([]string, len(a.textCanvasBuffer))
	copy(result, a.textCanvasBuffer)
	return result
}

// GetTextCanvasContentSince returns content since a given line count
func (a *App) GetTextCanvasContentSince(lastLineCount int) []string {
	a.textCanvasMutex.Lock()
	defer a.textCanvasMutex.Unlock()

	// If we have new content since the last check
	if len(a.textCanvasBuffer) > lastLineCount {
		// Return only the new lines
		newLines := a.textCanvasBuffer[lastLineCount:]
		result := make([]string, len(newLines))
		copy(result, newLines)
		return result
	}

	// No new content
	return []string{}
}

// GetTextCanvasLineCount returns just the current line count (lightweight check)
func (a *App) GetTextCanvasLineCount() int {
	a.textCanvasMutex.Lock()
	defer a.textCanvasMutex.Unlock()
	return len(a.textCanvasBuffer)
}

// startLogFileTailing starts tailing the localscribe log file
func (a *App) startLogFileTailing(logPath string) error {
	a.logFilePath = logPath
	fmt.Printf("Opening log file for tailing: %s\n", logPath)

	// Open the log file
	file, err := os.Open(logPath)
	if err != nil {
		return fmt.Errorf("failed to open log file: %w", err)
	}

	// Seek to end of file to only read new content
	offset, err := file.Seek(0, 2) // Seek to end
	if err != nil {
		file.Close()
		return fmt.Errorf("failed to seek to end of log file: %w", err)
	}

	fmt.Printf("Log file opened, starting at offset: %d\n", offset)

	// Check if there's already content in the file
	if offset > 0 {
		fmt.Printf("Log file already has %d bytes, reading existing content...\n", offset)
		// Seek back to beginning to read existing content
		file.Seek(0, 0)
		scanner := bufio.NewScanner(file)
		lineCount := 0
		for scanner.Scan() {
			line := scanner.Text()
			if line != "" {
				lineCount++
				fmt.Printf("Existing line %d: %s\n", lineCount, line)
				a.addLogLineToBuffer(line)
			}
		}
		fmt.Printf("Read %d existing lines from log file\n", lineCount)
		// Seek back to end for tailing
		file.Seek(0, 2)
	}

	a.logFileWatcher = file
	a.logFileOffset = offset

	// Start a goroutine to tail the file
	go a.tailLogFile()

	return nil
}

// tailLogFile continuously reads new lines from the log file
func (a *App) tailLogFile() {
	if a.logFileWatcher == nil {
		fmt.Printf("Log file watcher is nil, cannot tail\n")
		return
	}

	fmt.Printf("Starting log file tailing loop\n")
	lineCount := 0

	for {
		// Check if localscribe is still running
		a.localscribeMutex.Lock()
		running := a.localscribeRunning
		a.localscribeMutex.Unlock()

		if !running {
			fmt.Printf("Localscribe stopped, ending log file tailing\n")
			break
		}

		// Get current file size
		fileInfo, err := os.Stat(a.logFilePath)
		if err != nil {
			fmt.Printf("Error getting file info: %v\n", err)
			time.Sleep(1 * time.Second)
			continue
		}

		currentSize := fileInfo.Size()
		if currentSize <= a.logFileOffset {
			// No new content
			time.Sleep(1 * time.Second)
			continue
		}

		// Open file and read from our last position
		file, err := os.Open(a.logFilePath)
		if err != nil {
			fmt.Printf("Error reopening log file: %v\n", err)
			time.Sleep(1 * time.Second)
			continue
		}

		// Seek to our last known position
		file.Seek(a.logFileOffset, 0)

		// Read all new content
		newContent := make([]byte, currentSize-a.logFileOffset)
		bytesRead, err := file.Read(newContent)
		if err != nil {
			fmt.Printf("Error reading new content: %v\n", err)
			file.Close()
			time.Sleep(1 * time.Second)
			continue
		}

		file.Close()

		// Update our offset
		a.logFileOffset = currentSize

		// Split content into lines and process
		lines := strings.Split(string(newContent[:bytesRead]), "\n")
		newLines := 0
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if line != "" {
				lineCount++
				newLines++
				fmt.Printf("Tailed line %d: %s\n", lineCount, line)
				// Add the line to our buffer
				a.addLogLineToBuffer(line)
			}
		}

		if newLines > 0 {
			fmt.Printf("Added %d new lines to buffer\n", newLines)
		}

		// Sleep briefly before checking for more content
		time.Sleep(300 * time.Millisecond)
	}

	fmt.Printf("Log file tailing ended, processed %d total lines\n", lineCount)

	// Clean up
	if a.logFileWatcher != nil {
		a.logFileWatcher.Close()
		a.logFileWatcher = nil
	}
}

// addLogLineToBuffer adds a line from the log file to the text canvas buffer
func (a *App) addLogLineToBuffer(line string) {
	a.textCanvasMutex.Lock()

	// Add to buffer (line already has timestamp from localscribe)
	a.textCanvasBuffer = append(a.textCanvasBuffer, line)

	// Keep only the last 10,000 lines
	if len(a.textCanvasBuffer) > 10000 {
		a.textCanvasBuffer = a.textCanvasBuffer[len(a.textCanvasBuffer)-10000:]
	}

	// Get the current buffer for the event
	bufferCopy := make([]string, len(a.textCanvasBuffer))
	copy(bufferCopy, a.textCanvasBuffer)

	a.textCanvasMutex.Unlock()

	// Emit event to frontend - no polling needed!
	// Note: We'll implement this as a simple approach first
	// The frontend can still do minimal polling as a fallback
}

// ClearTextCanvas clears the text canvas buffer
func (a *App) ClearTextCanvas() {
	a.textCanvasMutex.Lock()
	defer a.textCanvasMutex.Unlock()
	a.textCanvasBuffer = make([]string, 0)
}

// GetTextCanvasStats returns statistics about the text canvas buffer
func (a *App) GetTextCanvasStats() map[string]interface{} {
	a.textCanvasMutex.Lock()
	defer a.textCanvasMutex.Unlock()

	totalChars := 0
	for _, line := range a.textCanvasBuffer {
		totalChars += len(line)
	}

	return map[string]interface{}{
		"lines":     len(a.textCanvasBuffer),
		"totalSize": totalChars,
		"sizeKB":    float64(totalChars) / 1024.0,
	}
}

// createSessionLogFile creates a timestamped log file for the session
func (a *App) createSessionLogFile() (string, error) {
	// Get home directory
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", fmt.Errorf("failed to get home directory: %w", err)
	}

	// Create log directory if it doesn't exist
	logDir := filepath.Join(homeDir, ".local", "scribe")
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create log directory: %w", err)
	}

	// Generate timestamped filename
	timestamp := time.Now().Format("20060102_1504")
	logFileName := fmt.Sprintf("transcription-%s.log", timestamp)
	logFilePath := filepath.Join(logDir, logFileName)

	// Touch the file to create it
	file, err := os.Create(logFilePath)
	if err != nil {
		return "", fmt.Errorf("failed to create log file: %w", err)
	}
	file.Close()

	fmt.Printf("Created session log file: %s\n", logFilePath)
	return logFilePath, nil
}

// TestEmbeddedBinary tests if the embedded localscribe binary works
func (a *App) TestEmbeddedBinary() string {
	path, err := a.GetEmbeddedBinaryPath("localscribe")
	if err != nil {
		return fmt.Sprintf("Failed to get embedded binary: %v", err)
	}

	// Test with --help first
	cmd := exec.Command(path, "--help")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Sprintf("Binary test failed: %v\nOutput: %s", err, string(output))
	}

	result := fmt.Sprintf("Binary test successful!\nPath: %s\nHelp output: %s\n", path, string(output))

	// Test with API key environment variable
	a.settingsMutex.Lock()
	assemblyAIKey := a.settings.AssemblyAIKey
	a.settingsMutex.Unlock()

	if assemblyAIKey != "" {
		result += fmt.Sprintf("\nTesting with API key (%s)...\n", a.maskAPIKey(assemblyAIKey))

		// Test a quick command that would use the API key
		testCmd := exec.Command(path, "--version") // or another safe command
		testCmd.Env = append(os.Environ(), "ASSEMBLYAI_API_KEY="+assemblyAIKey)
		testOutput, testErr := testCmd.CombinedOutput()

		if testErr != nil {
			result += fmt.Sprintf("API key test failed: %v\nOutput: %s", testErr, string(testOutput))
		} else {
			result += fmt.Sprintf("API key test successful!\nOutput: %s", string(testOutput))
		}
	} else {
		result += "\nNo API key set - skipping API key test"
	}

	return result
}

// GetSettings returns the current application settings
func (a *App) GetSettings() AppSettings {
	a.settingsMutex.Lock()
	defer a.settingsMutex.Unlock()

	// Return a copy with masked API keys for UI display
	settings := a.settings
	settings.GeminiAPIKey = a.maskAPIKey(settings.GeminiAPIKey)
	settings.AssemblyAIKey = a.maskAPIKey(settings.AssemblyAIKey)
	return settings
}

// SaveSettings saves the application settings
func (a *App) SaveSettings(settings AppSettings) string {
	a.settingsMutex.Lock()
	defer a.settingsMutex.Unlock()

	// Validate D&D version
	if settings.DNDVersion != "2014" && settings.DNDVersion != "2024" {
		return "Invalid D&D version. Must be '2014' or '2024'"
	}

	// Handle Gemini API key
	if settings.GeminiAPIKey == "" {
		// Empty string means use environment variable
		settings.GeminiAPIKey = os.Getenv("GEMINI_API_KEY")
	} else if a.isAPIKeyMasked(settings.GeminiAPIKey) {
		// If API key is masked (contains asterisks), keep the existing key
		settings.GeminiAPIKey = a.settings.GeminiAPIKey
	}
	// Otherwise, use the provided key as-is

	// Handle AssemblyAI API key
	if settings.AssemblyAIKey == "" {
		// Empty string means use environment variable
		settings.AssemblyAIKey = os.Getenv("ASSEMBLYAI_API_KEY")
	} else if a.isAPIKeyMasked(settings.AssemblyAIKey) {
		// If API key is masked (contains asterisks), keep the existing key
		settings.AssemblyAIKey = a.settings.AssemblyAIKey
	}
	// Otherwise, use the provided key as-is

	// Update settings
	a.settings = settings

	// TODO: Persist settings to file or preferences

	return "Settings saved successfully"
}

// isAPIKeyMasked checks if an API key is masked (contains asterisks)
func (a *App) isAPIKeyMasked(apiKey string) bool {
	return strings.Contains(apiKey, "*")
}

// maskAPIKey masks an API key for display, showing first 3 and last 4 characters
// with the middle filled with asterisks to preserve the original length
func (a *App) maskAPIKey(apiKey string) string {
	if apiKey == "" {
		return ""
	}

	if len(apiKey) <= 7 {
		// If key is too short, just show asterisks matching the length
		return strings.Repeat("*", len(apiKey))
	}

	// Show first 3 and last 4 characters with asterisks filling the middle
	middleLength := len(apiKey) - 7 // Total length minus first 3 and last 4
	return apiKey[:3] + strings.Repeat("*", middleLength) + apiKey[len(apiKey)-4:]
}
