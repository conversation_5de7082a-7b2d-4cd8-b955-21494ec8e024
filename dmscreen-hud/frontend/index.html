<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>DM Screen HUD</title>
</head>
<body>
<div id="app">
    <div id="hud-container">
        <div id="hud-header">
            <h2>DM Screen</h2>
            <div id="hud-controls">
                <button id="clear-btn" title="Clear all entries">🗑️</button>
                <button id="settings-btn" title="Settings">⚙️</button>
            </div>
        </div>
        <div id="hud-main">
            <!-- Left side: 60% - Text Canvas for localscribe logging -->
            <div id="text-canvas-panel">
                <div id="text-canvas-header">
                    <h3>Localscribe Log</h3>
                    <button id="clear-log-btn" title="Clear log">🗑️</button>
                </div>
                <div id="text-canvas">
                    <!-- Localscribe text will be piped here -->
                </div>
            </div>

            <!-- Right side: 40% - Widget streams and command area -->
            <div id="right-panel">
                <!-- Session Timer -->
                <div id="session-timer">
                    <div id="timer-display">00:00:00</div>
                    <button id="timer-toggle" class="timer-btn">Elapsed</button>
                </div>

                <!-- Widget streams -->
                <div id="widget-streams">
                    <div id="message-list">
                        <!-- Messages will be dynamically added here -->
                    </div>
                </div>

                <!-- Bottom 10% of right panel: Command area -->
                <div id="command-area">
                    <input type="text" id="command-input" placeholder="Enter command or search..." />
                    <button id="execute-btn" title="Execute">▶</button>
                </div>
            </div>
        </div>
        <div id="hud-footer">
            <div id="session-controls">
                <button id="start-session-btn" class="session-btn start">▶ Start Session</button>
                <button id="stop-session-btn" class="session-btn stop" disabled>⏹ Stop Session</button>
                <div id="session-status">Ready</div>
            </div>
        </div>
    </div>

    <!-- Preferences Modal -->
    <div id="preferences-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Preferences</h3>
                <button type="button" id="preferences-close" class="close-btn" title="Close">✕</button>
            </div>
            <div class="modal-body">
                <form id="preferences-form">
                    <div class="form-group">
                        <label for="dnd-version">D&D Version:</label>
                        <div class="toggle-group">
                            <input type="radio" id="dnd-2014" name="dnd-version" value="2014">
                            <label for="dnd-2014" class="toggle-label">2014</label>
                            <input type="radio" id="dnd-2024" name="dnd-version" value="2024">
                            <label for="dnd-2024" class="toggle-label">2024</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="gemini-api-key">Gemini API Key:</label>
                        <input type="text" id="gemini-api-key" name="gemini-api-key" placeholder="Enter your Gemini API key">
                        <small class="form-help">Leave blank to use GEMINI_API_KEY environment variable</small>
                    </div>
                    <div class="form-group">
                        <label for="assemblyai-api-key">AssemblyAI API Key:</label>
                        <input type="text" id="assemblyai-api-key" name="assemblyai-api-key" placeholder="Enter your AssemblyAI API key">
                        <small class="form-help">Required for transcription. Leave blank to use ASSEMBLYAI_API_KEY environment variable</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="preferences-save" class="btn btn-primary">Save</button>
                <button type="button" id="preferences-cancel" class="btn btn-secondary">Cancel</button>
            </div>
        </div>
    </div>
</div>
<script src="./src/main.js" type="module"></script>
</body>
</html>
