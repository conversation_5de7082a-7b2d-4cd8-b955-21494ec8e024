// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

export function AddMessage(arg1, arg2) {
  return window['go']['main']['App']['AddMessage'](arg1, arg2);
}

export function CleanupEmbeddedBinaries() {
  return window['go']['main']['App']['CleanupEmbeddedBinaries']();
}

export function ClearMessages() {
  return window['go']['main']['App']['ClearMessages']();
}

export function ClearTextCanvas() {
  return window['go']['main']['App']['ClearTextCanvas']();
}

export function EnableHotkeyDebug() {
  return window['go']['main']['App']['EnableHotkeyDebug']();
}

export function GetEmbeddedBinaryPath(arg1) {
  return window['go']['main']['App']['GetEmbeddedBinaryPath'](arg1);
}

export function GetMessages() {
  return window['go']['main']['App']['GetMessages']();
}

export function GetSettings() {
  return window['go']['main']['App']['GetSettings']();
}

export function GetTextCanvasContent() {
  return window['go']['main']['App']['GetTextCanvasContent']();
}

export function GetTextCanvasContentSince(arg1) {
  return window['go']['main']['App']['GetTextCanvasContentSince'](arg1);
}

export function GetTextCanvasLineCount() {
  return window['go']['main']['App']['GetTextCanvasLineCount']();
}

export function GetTextCanvasStats() {
  return window['go']['main']['App']['GetTextCanvasStats']();
}

export function GetVisibilityState() {
  return window['go']['main']['App']['GetVisibilityState']();
}

export function IsLocalscribeRunning() {
  return window['go']['main']['App']['IsLocalscribeRunning']();
}

export function ListEmbeddedBinaries() {
  return window['go']['main']['App']['ListEmbeddedBinaries']();
}

export function LogMessage(arg1) {
  return window['go']['main']['App']['LogMessage'](arg1);
}

export function SaveSettings(arg1) {
  return window['go']['main']['App']['SaveSettings'](arg1);
}

export function SendToGallery(arg1) {
  return window['go']['main']['App']['SendToGallery'](arg1);
}

export function StartLocalscribe() {
  return window['go']['main']['App']['StartLocalscribe']();
}

export function StopLocalscribe() {
  return window['go']['main']['App']['StopLocalscribe']();
}

export function ToggleVisibility() {
  return window['go']['main']['App']['ToggleVisibility']();
}
