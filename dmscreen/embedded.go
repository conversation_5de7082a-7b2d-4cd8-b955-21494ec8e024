package main

import (
	"embed"
	"fmt"
	"io/fs"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
)

//go:embed assets/bin/*
var embeddedBinaries embed.FS

// EmbeddedBinary represents an embedded binary tool
type EmbeddedBinary struct {
	Name     string
	Path     string
	Platform string
}

// GetEmbeddedBinaries returns a list of available embedded binaries
func GetEmbeddedBinaries() []EmbeddedBinary {
	var binaries []EmbeddedBinary

	// Walk through embedded binaries
	fs.WalkDir(embeddedBinaries, "assets/bin", func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		if !d.IsDir() {
			name := d.Name()
			binaries = append(binaries, EmbeddedBinary{
				Name:     name,
				Path:     path,
				Platform: runtime.GOOS + "/" + runtime.GOARCH,
			})
		}
		return nil
	})

	return binaries
}

// ExtractEmbeddedBinary extracts an embedded binary to a temporary location
func ExtractEmbeddedBinary(name string) (string, error) {
	// Find the binary in embedded files
	binaryPath := fmt.Sprintf("assets/bin/%s", name)

	data, err := embeddedBinaries.ReadFile(binaryPath)
	if err != nil {
		return "", fmt.Errorf("binary %s not found in embedded assets: %w", name, err)
	}

	// Create temporary file
	tempDir := os.TempDir()
	tempFile := filepath.Join(tempDir, name)

	// Write binary to temp location
	err = os.WriteFile(tempFile, data, 0755)
	if err != nil {
		return "", fmt.Errorf("failed to write binary to temp location: %w", err)
	}

	// Verify the binary is executable
	if _, err := os.Stat(tempFile); err != nil {
		return "", fmt.Errorf("extracted binary is not accessible: %w", err)
	}

	// On macOS, try to remove quarantine attribute that might block execution
	if runtime.GOOS == "darwin" {
		// Try to remove quarantine attribute (this might fail, but that's ok)
		removeQuarantineCmd := exec.Command("xattr", "-d", "com.apple.quarantine", tempFile)
		removeQuarantineCmd.Run() // Ignore errors
		fmt.Printf("Attempted to remove quarantine attribute from %s\n", tempFile)
	}

	fmt.Printf("Successfully extracted %s to %s (%d bytes)\n", name, tempFile, len(data))
	return tempFile, nil
}

// ExtractAllEmbeddedBinaries extracts all embedded binaries to a directory
func ExtractAllEmbeddedBinaries(targetDir string) error {
	// Create target directory if it doesn't exist
	err := os.MkdirAll(targetDir, 0755)
	if err != nil {
		return fmt.Errorf("failed to create target directory: %w", err)
	}

	// Walk through all embedded binaries
	return fs.WalkDir(embeddedBinaries, "assets/bin", func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		if !d.IsDir() {
			// Read embedded file
			data, err := embeddedBinaries.ReadFile(path)
			if err != nil {
				return fmt.Errorf("failed to read embedded file %s: %w", path, err)
			}

			// Write to target directory
			targetPath := filepath.Join(targetDir, d.Name())
			err = os.WriteFile(targetPath, data, 0755)
			if err != nil {
				return fmt.Errorf("failed to write file %s: %w", targetPath, err)
			}

			fmt.Printf("Extracted embedded binary: %s\n", targetPath)
		}
		return nil
	})
}

// GetEmbeddedBinaryPath returns the path to an extracted embedded binary
// If the binary hasn't been extracted yet, it extracts it first
func (a *App) GetEmbeddedBinaryPath(binaryName string) (string, error) {
	// Check if we have a cached path
	if a.embeddedBinaryPaths == nil {
		a.embeddedBinaryPaths = make(map[string]string)
	}

	if path, exists := a.embeddedBinaryPaths[binaryName]; exists {
		// Check if file still exists
		if _, err := os.Stat(path); err == nil {
			return path, nil
		}
		// File was deleted, remove from cache
		delete(a.embeddedBinaryPaths, binaryName)
	}

	// Extract the binary
	path, err := ExtractEmbeddedBinary(binaryName)
	if err != nil {
		return "", err
	}

	// Cache the path
	a.embeddedBinaryPaths[binaryName] = path
	return path, nil
}

// CleanupEmbeddedBinaries removes extracted temporary binaries
func (a *App) CleanupEmbeddedBinaries() {
	if a.embeddedBinaryPaths == nil {
		return
	}

	for name, path := range a.embeddedBinaryPaths {
		if err := os.Remove(path); err != nil {
			fmt.Printf("Warning: failed to cleanup embedded binary %s at %s: %v\n", name, path, err)
		}
	}

	a.embeddedBinaryPaths = make(map[string]string)
}

// ListEmbeddedBinaries returns a list of embedded binaries for the frontend
func (a *App) ListEmbeddedBinaries() []EmbeddedBinary {
	return GetEmbeddedBinaries()
}
