(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))i(s);new MutationObserver(s=>{for(const a of s)if(a.type==="childList")for(const n of a.addedNodes)n.tagName==="LINK"&&n.rel==="modulepreload"&&i(n)}).observe(document,{childList:!0,subtree:!0});function t(s){const a={};return s.integrity&&(a.integrity=s.integrity),s.referrerpolicy&&(a.referrerPolicy=s.referrerpolicy),s.crossorigin==="use-credentials"?a.credentials="include":s.crossorigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function i(s){if(s.ep)return;s.ep=!0;const a=t(s);fetch(s.href,a)}})();class r{constructor(){this.messages=[],this.logText="",this.messageList=document.getElementById("message-list"),this.statusElement=document.getElementById("session-status"),this.clearBtn=document.getElementById("clear-btn"),this.settingsBtn=document.getElementById("settings-btn"),this.textCanvas=document.getElementById("text-canvas"),this.clearLogBtn=document.getElementById("clear-log-btn"),this.commandInput=document.getElementById("command-input"),this.executeBtn=document.getElementById("execute-btn"),this.startSessionBtn=document.getElementById("start-session-btn"),this.stopSessionBtn=document.getElementById("stop-session-btn"),this.testBinaryBtn=document.getElementById("test-binary-btn"),this.textCanvasUpdateInterval=null,this.preferencesModal=document.getElementById("preferences-modal"),this.preferencesForm=document.getElementById("preferences-form"),this.preferencesClose=document.getElementById("preferences-close"),this.preferencesSave=document.getElementById("preferences-save"),this.preferencesCancel=document.getElementById("preferences-cancel"),this.dndVersionRadios=document.querySelectorAll('input[name="dnd-version"]'),this.geminiApiKeyInput=document.getElementById("gemini-api-key"),this.assemblyAIApiKeyInput=document.getElementById("assemblyai-api-key"),this.sessionActive=!1,this.lastContentLength=0,this.timerDisplay=document.getElementById("timer-display"),this.timerToggle=document.getElementById("timer-toggle"),this.sessionStartTime=null,this.timerInterval=null,this.showElapsed=!0,this.sessionDuration=4*60*60*1e3,this.initializeEventListeners(),this.updateStatus("Ready"),this.addSampleMessages(),this.addSampleLogText()}initializeEventListeners(){this.clearBtn.addEventListener("click",()=>this.clearMessages()),this.settingsBtn.addEventListener("click",()=>this.showSettings()),this.clearLogBtn.addEventListener("click",()=>this.clearLog()),this.executeBtn.addEventListener("click",()=>this.executeCommand()),this.startSessionBtn.addEventListener("click",()=>this.startSession()),this.stopSessionBtn.addEventListener("click",()=>this.stopSession()),this.testBinaryBtn.addEventListener("click",()=>this.testEmbeddedBinary()),this.timerToggle.addEventListener("click",()=>this.toggleTimerMode()),this.preferencesClose.addEventListener("click",e=>{e.preventDefault(),this.closePreferences()}),this.preferencesSave.addEventListener("click",e=>{e.preventDefault(),this.savePreferences()}),this.preferencesCancel.addEventListener("click",e=>{e.preventDefault(),this.closePreferences()}),this.preferencesModal.addEventListener("click",e=>{e.target===this.preferencesModal&&this.closePreferences()}),this.geminiApiKeyInput.addEventListener("blur",()=>this.maskApiKeyInput(this.geminiApiKeyInput)),this.assemblyAIApiKeyInput.addEventListener("blur",()=>this.maskApiKeyInput(this.assemblyAIApiKeyInput)),this.preferencesForm.addEventListener("submit",e=>(e.preventDefault(),!1)),this.commandInput.addEventListener("keydown",e=>{e.key==="Enter"&&this.executeCommand()})}addMessage(e,t,i=null){const s={id:Date.now(),type:e,content:t,imagePath:i,timestamp:new Date};this.messages.unshift(s),this.renderMessage(s),this.updateStatus(`${this.messages.length} messages`)}renderMessage(e){const t=document.createElement("div");t.className="message-item",t.dataset.messageId=e.id,t.innerHTML=`
            <div class="message-header">
                <span class="message-type">${e.type}</span>
                <span class="message-time">${this.formatTime(e.timestamp)}</span>
            </div>
            <div class="message-content">${e.content}</div>
            <div class="message-actions">
                <button class="action-btn gallery" onclick="hud.sendToGallery(${e.id})">\u{1F4F8} Gallery</button>
                <button class="action-btn log" onclick="hud.logMessage(${e.id})">\u{1F4DD} Log</button>
            </div>
        `,this.messageList.insertBefore(t,this.messageList.firstChild)}formatTime(e){return e.toLocaleTimeString("en-US",{hour12:!1,hour:"2-digit",minute:"2-digit"})}clearMessages(){this.messages=[],this.messageList.innerHTML="",this.updateStatus("Messages cleared")}sendToGallery(e){const t=this.messages.find(i=>i.id===e);t&&(console.log("Sending to gallery:",t),this.updateStatus(`Sent "${t.type}" to gallery`))}logMessage(e){const t=this.messages.find(i=>i.id===e);t&&(console.log("Logging message:",t),this.updateStatus(`Logged "${t.type}" message`))}async showSettings(){try{const e=await window.go.main.App.GetSettings();this.populatePreferencesForm(e),this.preferencesModal.classList.remove("hidden")}catch(e){console.error("Failed to load settings:",e),this.updateStatus("Failed to load settings")}}closePreferences(){this.preferencesModal.classList.add("hidden")}async savePreferences(){try{const e=this.getPreferencesFromForm(),t=await window.go.main.App.SaveSettings(e);t.includes("successfully")?(this.updateStatus("Settings saved"),this.closePreferences()):this.updateStatus(t)}catch(e){console.error("Failed to save settings:",e),this.updateStatus("Failed to save settings")}}populatePreferencesForm(e){this.dndVersionRadios.forEach(t=>{t.checked=t.value===e.dndVersion}),this.geminiApiKeyInput.value=e.geminiAPIKey||"",this.geminiApiKeyInput.dataset.isMasked=e.geminiAPIKey&&e.geminiAPIKey.includes("***")?"true":"false",this.geminiApiKeyInput.dataset.hasValue=e.geminiAPIKey?"true":"false",this.assemblyAIApiKeyInput.value=e.assemblyAIKey||"",this.assemblyAIApiKeyInput.dataset.isMasked=e.assemblyAIKey&&e.assemblyAIKey.includes("***")?"true":"false",this.assemblyAIApiKeyInput.dataset.hasValue=e.assemblyAIKey?"true":"false"}getPreferencesFromForm(){const e=document.querySelector('input[name="dnd-version"]:checked'),t=e?e.value:"2024";let i=this.geminiApiKeyInput.value.trim(),s=this.assemblyAIApiKeyInput.value.trim();return{dndVersion:t,geminiAPIKey:i,assemblyAIKey:s}}maskApiKeyInput(e){const t=e.value.trim();if(t&&!t.includes("*")&&t.length>7){const i=t.length-7,s=t.substring(0,3)+"*".repeat(i)+t.substring(t.length-4);e.value=s,e.dataset.originalValue=t}else if(t&&!t.includes("*")&&t.length<=7){const i="*".repeat(t.length);e.value=i,e.dataset.originalValue=t}}appendToLog(e){this.logText+=e+`
`,this.textCanvas.textContent=this.logText,this.textCanvas.scrollTop=this.textCanvas.scrollHeight}async clearLog(){try{await window.go.main.App.ClearTextCanvas(),this.logText="",this.textCanvas.textContent="",this.updateStatus("Log cleared")}catch(e){console.error("Failed to clear log:",e),this.updateStatus("Clear failed")}}startPeriodicRefresh(){if(!this.sessionActive)return;const e=1e4;this.textCanvasUpdateInterval=setInterval(async()=>{if(!this.sessionActive){this.stopPeriodicRefresh();return}await this.refreshTextCanvas()},e)}stopPeriodicRefresh(){this.textCanvasUpdateInterval&&(clearInterval(this.textCanvasUpdateInterval),this.textCanvasUpdateInterval=null)}executeCommand(){const e=this.commandInput.value.trim();!e||(this.updateStatus(`Executing: ${e}`),this.appendToLog(`> ${e}`),this.processCommand(e),this.commandInput.value="")}async processCommand(e){const t=e.toLowerCase();if(t.startsWith("search ")){const i=e.substring(7);await this.sendToLocalscribeMetadata(`search command: "${i}"`)}else if(t==="clear")this.clearMessages(),await this.sendToLocalscribeMetadata("messages cleared via command");else if(t==="stats")try{const i=await window.go.main.App.GetTextCanvasStats();await this.sendToLocalscribeMetadata(`buffer stats: ${i.lines} lines, ${i.sizeKB.toFixed(1)} KB`)}catch{await this.sendToLocalscribeMetadata("failed to get buffer stats")}else t==="help"?await this.sendToLocalscribeMetadata("help requested - available commands: search, clear, stats, help"):await this.sendToLocalscribeMetadata(`unknown command: "${e}"`)}async sendToLocalscribeMetadata(e){try{const t=await fetch("http://localhost:8080/metadata?body="+encodeURIComponent(e));t.ok||console.error("Failed to send metadata to localscribe:",t.statusText)}catch(t){console.error("Failed to send metadata to localscribe:",t),this.updateStatus(`Command: ${e}`)}}async startSession(){this.updateStatus("Starting session..."),this.startSessionBtn.disabled=!0;try{const e=await window.go.main.App.StartLocalscribe();console.log(`Session started: ${e}`),this.updateStatus("Session active"),this.startSessionBtn.disabled=!0,this.stopSessionBtn.disabled=!1,this.sessionActive=!0,this.startTimer(),this.startPeriodicRefresh(),setTimeout(()=>this.refreshTextCanvas(),2e3)}catch(e){console.error(`Failed to start session: ${e}`),this.updateStatus("Start failed"),this.startSessionBtn.disabled=!1}}async refreshTextCanvas(){try{const e=await window.go.main.App.GetTextCanvasLineCount();if(e>this.lastContentLength){const t=await window.go.main.App.GetTextCanvasContentSince(this.lastContentLength);t&&t.length>0&&(this.logText?this.logText+=`
`+t.join(`
`):this.logText=t.join(`
`),this.textCanvas.textContent=this.logText,this.lastContentLength=e,this.textCanvas.scrollTop=this.textCanvas.scrollHeight)}}catch(e){console.debug("Manual refresh error:",e)}}async stopSession(){this.updateStatus("Stopping session..."),this.stopSessionBtn.disabled=!0;try{const e=await window.go.main.App.StopLocalscribe();console.log(`Session stopped: ${e}`),this.updateStatus("Ready"),this.startSessionBtn.disabled=!1,this.stopSessionBtn.disabled=!0,this.sessionActive=!1,this.stopTimer(),this.stopPeriodicRefresh()}catch(e){console.error(`Failed to stop session: ${e}`),this.updateStatus("Stop failed"),this.stopSessionBtn.disabled=!1}}async testEmbeddedBinary(){try{this.updateStatus("Testing embedded binary...");const e=await window.go.main.App.TestEmbeddedBinary();alert(`Binary Test Result:

`+e),this.updateStatus("Binary test completed")}catch(e){console.error("Failed to test binary:",e),alert(`Binary Test Failed:

`+e),this.updateStatus("Binary test failed")}}updateStatus(e){this.statusElement.textContent=e,setTimeout(()=>{this.statusElement.textContent="Ready"},3e3)}addSampleMessages(){setTimeout(()=>{this.addMessage("NPC",'Bartender Gareth: "Welcome to the Prancing Pony, travelers!"')},1e3),setTimeout(()=>{this.addMessage("LORE","The ancient ruins of Shadowmere are said to contain powerful artifacts.")},2e3),setTimeout(()=>{this.addMessage("DC","Perception DC 15 to notice the hidden door behind the tapestry.")},3e3)}addSampleLogText(){setTimeout(()=>{this.appendToLog("[13:24:15] Session started - The Prancing Pony"),this.appendToLog("[13:24:16] Players entered the tavern"),this.appendToLog('[13:24:18] Gareth (Bartender): "What can I get you folks?"'),this.appendToLog("[13:24:22] Thorin rolled Persuasion: 15 (success)"),this.appendToLog("[13:24:25] Gareth reveals information about the ruins"),this.appendToLog("[13:24:30] Party decides to investigate Shadowmere")},500)}startTimer(){this.resetTimer(),this.sessionStartTime=Date.now(),this.timerInterval=setInterval(()=>this.updateTimer(),1e3),this.updateTimer()}stopTimer(){this.timerInterval&&(clearInterval(this.timerInterval),this.timerInterval=null)}resetTimer(){this.sessionStartTime=null,this.timerDisplay.textContent="00:00:00",this.timerDisplay.className=""}updateTimer(){if(!this.sessionStartTime)return;const t=Date.now()-this.sessionStartTime;let i,s="";this.showElapsed?i=t:i=Math.max(0,this.sessionDuration-t);const a=t/this.sessionDuration;a>=.75?s="danger":a>=.5&&(s="warning"),this.timerDisplay.textContent=this.formatTime(i),this.timerDisplay.className=s}formatTime(e){const t=Math.floor(e/1e3),i=Math.floor(t/3600),s=Math.floor(t%3600/60),a=t%60;return`${i.toString().padStart(2,"0")}:${s.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`}toggleTimerMode(){this.showElapsed=!this.showElapsed,this.timerToggle.textContent=this.showElapsed?"Elapsed":"Remaining",this.updateTimer()}destroy(){this.stopPeriodicRefresh(),this.stopTimer()}}document.addEventListener("DOMContentLoaded",()=>{window.hud=new r});window.addEventListener("beforeunload",()=>{window.hud&&window.hud.destroy()});
