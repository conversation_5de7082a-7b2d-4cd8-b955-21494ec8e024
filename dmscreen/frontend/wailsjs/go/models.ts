export namespace main {
	
	export class AppSettings {
	    dndVersion: string;
	    geminiAPIKey: string;
	    assemblyAIKey: string;
	
	    static createFrom(source: any = {}) {
	        return new AppSettings(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.dndVersion = source["dndVersion"];
	        this.geminiAPIKey = source["geminiAPIKey"];
	        this.assemblyAIKey = source["assemblyAIKey"];
	    }
	}
	export class EmbeddedBinary {
	    Name: string;
	    Path: string;
	    Platform: string;
	
	    static createFrom(source: any = {}) {
	        return new EmbeddedBinary(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.Name = source["Name"];
	        this.Path = source["Path"];
	        this.Platform = source["Platform"];
	    }
	}
	export class HUDMessage {
	    id: number;
	    type: string;
	    content: string;
	    imagePath?: string;
	    // Go type: time
	    timestamp: any;
	
	    static createFrom(source: any = {}) {
	        return new HUDMessage(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.id = source["id"];
	        this.type = source["type"];
	        this.content = source["content"];
	        this.imagePath = source["imagePath"];
	        this.timestamp = this.convertValues(source["timestamp"], null);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}

}

