package main

import (
	"context"
	"log"

	hook "github.com/robotn/gohook"
	"github.com/wailsapp/wails/v2/pkg/runtime"
)

// Debug flag for hotkey logging
var DebugHotkeys = false

// HotkeyCallback is called when the hotkey is pressed
type HotkeyCallback func()

// KeyState tracks the current state of modifier keys
type KeyState struct {
	CmdPressed bool
}

// StartHotkeyListener starts listening for the global hotkey Cmd+D
func StartHotkeyListener(ctx context.Context, callback HotkeyCallback) error {
	if DebugHotkeys {
		log.Println("Starting hotkey detection for Cmd+D")
	}

	// Track key states
	keyState := &KeyState{}

	// Start the hook in a separate goroutine
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("Hook panic recovered: %v", r)
			}
			log.Println("Hook goroutine exiting")
		}()

		if DebugHotkeys {
			log.Println("Starting hook for Cmd+D detection...")
		}
		s := hook.Start()
		defer hook.End()

		if DebugHotkeys {
			log.Println("Hook started, listening for key events...")
		}
		for {
			select {
			case <-ctx.Done():
				log.Println("Context cancelled, stopping hotkey listener")
				return
			case event, ok := <-s:
				if !ok {
					log.Println("Hook channel closed, stopping listener")
					return
				}

				// Only process keyboard events, ignore mouse
				// Kind=3: KeyDown, Kind=4: KeyHold, Kind=5: KeyUp
				if event.Kind == 3 || event.Kind == 4 || event.Kind == 5 {
					if DebugHotkeys {
						log.Printf("Key event: Kind=%d, Rawcode=%d, Keychar=%c (%d)", event.Kind, event.Rawcode, event.Keychar, event.Keychar)
					}

					// Track modifier key states
					switch event.Rawcode {
					case 55, 54: // Left Cmd (55) or Right Cmd (54)
						// Set to true on KeyDown(3) or KeyHold(4), false on KeyUp(5)
						keyState.CmdPressed = (event.Kind == 3 || event.Kind == 4)
						if DebugHotkeys {
							log.Printf("Cmd key: %v (kind=%d, rawcode=%d)", keyState.CmdPressed, event.Kind, event.Rawcode)
						}
					default:
						// Check for D key by character (more reliable than rawcode)
						if event.Kind == 3 && (event.Keychar == 'd' || event.Keychar == 'D') {
							if DebugHotkeys {
								log.Printf("D key pressed (rawcode %d) - Cmd:%v", event.Rawcode, keyState.CmdPressed)
							}
							if keyState.CmdPressed {
								log.Println("🎯 HOTKEY DETECTED! Cmd+D combination pressed!")
								callback()
							}
						}
					}
				}
			}
		}
	}()

	if DebugHotkeys {
		log.Println("Cmd+D hotkey detection setup complete")
	}
	return nil
}

// ToggleWindowVisibility toggles the window visibility using Wails runtime
func ToggleWindowVisibility(ctx context.Context, isVisible *bool) {
	*isVisible = !*isVisible
	if *isVisible {
		runtime.WindowShow(ctx)
		log.Println("Showing HUD window")
	} else {
		runtime.WindowHide(ctx)
		log.Println("Hiding HUD window")
	}
}
