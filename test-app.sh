#!/bin/bash

echo "Testing dmscreen.app with console output..."
echo "=========================================="

# Run the app and capture output
./dmscreen/build/bin/dmscreen.app/Contents/MacOS/dmscreen 2>&1 | tee app-output.log &

APP_PID=$!
echo "App started with PID: $APP_PID"
echo "Console output will be saved to app-output.log"
echo "Press Ctrl+C to stop the app"

# Wait for the app to finish or be interrupted
wait $APP_PID
